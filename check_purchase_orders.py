#!/usr/bin/env python3
"""
检查数据库中的采购订单状态
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import PurchaseOrder, StockIn

def check_purchase_orders():
    """检查采购订单状态"""
    app = create_app()
    
    with app.app_context():
        print("=== 检查采购订单状态 ===")
        
        # 查询所有采购订单
        all_orders = PurchaseOrder.query.all()
        print(f"总采购订单数: {len(all_orders)}")
        
        # 按状态分组统计
        status_count = {}
        for order in all_orders:
            status = order.status
            if status in status_count:
                status_count[status] += 1
            else:
                status_count[status] = 1
        
        print("\n按状态统计:")
        for status, count in status_count.items():
            print(f"  {status}: {count} 个")
        
        # 查询已确认状态的采购订单
        confirmed_orders = PurchaseOrder.query.filter_by(status='已确认').all()
        print(f"\n已确认状态的采购订单: {len(confirmed_orders)} 个")
        
        for order in confirmed_orders:
            # 检查是否已经创建了入库单
            existing_stock_in = StockIn.query.filter_by(purchase_order_id=order.id).first()
            has_stock_in = "是" if existing_stock_in else "否"
            
            supplier_name = order.supplier.name if order.supplier else "自购"
            
            print(f"  订单号: {order.order_number}")
            print(f"    ID: {order.id}")
            print(f"    状态: {order.status}")
            print(f"    供应商: {supplier_name}")
            print(f"    创建时间: {order.created_at}")
            print(f"    已创建入库单: {has_stock_in}")
            if existing_stock_in:
                print(f"    入库单号: {existing_stock_in.stock_in_number}")
            print()
        
        # 如果没有已确认的订单，创建一个测试订单
        if len(confirmed_orders) == 0:
            print("没有找到已确认状态的采购订单，正在创建测试数据...")
            create_test_purchase_order()

def create_test_purchase_order():
    """创建测试采购订单"""
    from app.models import AdministrativeArea, Supplier, Ingredient, PurchaseOrderItem
    from datetime import datetime, date
    import uuid
    
    try:
        # 获取第一个区域
        area = AdministrativeArea.query.first()
        if not area:
            print("错误: 没有找到区域数据")
            return
        
        # 获取第一个供应商（如果有的话）
        supplier = Supplier.query.first()
        
        # 创建采购订单
        order_number = f"PO{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6]}"
        
        purchase_order = PurchaseOrder(
            order_number=order_number,
            area_id=area.id,
            order_date=date.today(),
            delivery_date=date.today(),
            status='已确认',
            supplier_id=supplier.id if supplier else None,
            notes='测试采购订单 - 用于入库向导测试'
        )
        
        db.session.add(purchase_order)
        db.session.flush()  # 获取ID
        
        # 添加一些测试食材
        ingredients = Ingredient.query.limit(3).all()
        for i, ingredient in enumerate(ingredients):
            item = PurchaseOrderItem(
                order_id=purchase_order.id,
                ingredient_id=ingredient.id,
                quantity=10.0 + i * 5,
                unit=ingredient.standard_unit or '公斤',
                unit_price=5.0 + i * 2
            )
            db.session.add(item)
        
        db.session.commit()
        
        print(f"✓ 成功创建测试采购订单: {order_number}")
        print(f"  订单ID: {purchase_order.id}")
        print(f"  区域: {area.name}")
        print(f"  供应商: {supplier.name if supplier else '自购'}")
        print(f"  食材数量: {len(ingredients)}")
        
    except Exception as e:
        db.session.rollback()
        print(f"✗ 创建测试采购订单失败: {str(e)}")

if __name__ == '__main__':
    check_purchase_orders()
