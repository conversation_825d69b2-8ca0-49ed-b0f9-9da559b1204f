// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 激活当前页面对应的导航项
    activateCurrentNavItem();

    // 自动关闭警告消息
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 初始化通知系统
    initNotificationSystem();

    // 初始化场景选择系统（如果页面包含场景选择元素）
    if (document.getElementById('scenarioSelectionModal')) {
        initScenarioSelection();
    }
});

// 激活当前页面对应的导航项
function activateCurrentNavItem() {
    const currentPath = window.location.pathname;

    // 处理下拉菜单项
    const dropdownItems = document.querySelectorAll('.dropdown-menu .dropdown-item');
    let activeDropdownFound = false;

    dropdownItems.forEach(function(item) {
        const href = item.getAttribute('href');
        // 检查路径是否匹配或者是子路径
        if (href && (href === currentPath || currentPath.startsWith(href + '/'))) {
            item.classList.add('active');
            activeDropdownFound = true;

            // 激活父菜单
            const dropdownMenu = item.closest('.dropdown-menu');
            if (dropdownMenu) {
                const dropdownToggle = dropdownMenu.previousElementSibling;
                if (dropdownToggle && dropdownToggle.classList.contains('dropdown-toggle')) {
                    dropdownToggle.classList.add('active');
                }
            }
        }
    });

    // 如果没有找到活动的下拉菜单项，检查顶级导航项
    if (!activeDropdownFound) {
        const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
        navLinks.forEach(function(link) {
            const href = link.getAttribute('href');
            if (href && href !== '#' && (href === currentPath || currentPath.startsWith(href + '/'))) {
                link.classList.add('active');
            }
        });
    }
}

// 初始化通知系统
function initNotificationSystem() {
    // 检查是否已登录（通过检查通知图标是否存在）
    const notificationDropdown = document.getElementById('notificationDropdown');
    if (!notificationDropdown) return;

    // 首次加载页面时检查通知
    checkNotifications();

    // 每60秒检查一次新通知
    setInterval(checkNotifications, 60000);
}

// 初始化场景选择系统
function initScenarioSelection() {
    let selectedScenario = null;

    // 场景卡片点击事件
    $('.scenario-card').click(function() {
        $('.scenario-card').removeClass('selected');
        $(this).addClass('selected');
        selectedScenario = $(this).data('scenario');
        $('#confirmScenarioBtn').prop('disabled', false);
    });

    // 确认选择按钮
    $('#confirmScenarioBtn').click(function() {
        if (selectedScenario) {
            startGuideWithScenario(selectedScenario);
        }
    });

    // 自动检测按钮
    $('#autoDetectBtn').click(function() {
        autoDetectSchoolType();
    });

    function startGuideWithScenario(schoolType) {
        // 开始场景化引导
        $.ajax({
            url: '/api/guide/start',
            type: 'POST',
            data: {
                school_type: schoolType
            },
            success: function(data) {
                if (data.success) {
                    $('#scenarioSelectionModal').modal('hide');

                    // 显示场景化欢迎消息
                    showScenarioWelcome(schoolType);

                    // 开始引导
                    if (typeof userGuide !== 'undefined') {
                        userGuide.schoolType = schoolType;
                        userGuide.showStep('welcome');
                    }
                } else {
                    alert('启动引导失败：' + data.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('启动引导失败:', error, 'Status:', status, 'XHR:', xhr);
                alert('启动引导失败，请稍后重试');
            }
        });
    }

    function autoDetectSchoolType() {
        // 获取学校名称（可以从用户信息或页面中获取）
        const schoolName = '朝阳区实验中学';

        $.ajax({
            url: '/api/guide/detect-school-type',
            type: 'POST',
            data: {
                school_name: schoolName
            },
            success: function(data) {
                if (data.success) {
                    const detectedType = data.detected_type;

                    // 高亮检测到的类型
                    $('.scenario-card').removeClass('selected');
                    $(`.scenario-card[data-scenario="${detectedType}"]`).addClass('selected');
                    selectedScenario = detectedType;
                    $('#confirmScenarioBtn').prop('disabled', false);

                    // 显示检测结果
                    alert(`根据您的学校信息，系统检测到您的学校类型为：${getScenarioName(detectedType)}\n\n如果检测结果正确，请点击"确认选择"开始引导。`);
                } else {
                    alert('自动检测失败：' + data.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('自动检测失败:', error);
                alert('自动检测失败，请手动选择学校类型');
            }
        });
    }

    function getScenarioName(scenario) {
        const names = {
            'primary': '小学',
            'middle': '中学',
            'high': '高中',
            'vocational': '职业学校',
            'university': '大学',
            'rural': '乡村学校'
        };
        return names[scenario] || '未知类型';
    }

    function showScenarioWelcome(schoolType) {
        // 获取场景化欢迎消息
        $.ajax({
            url: `/api/guide/scenario/${schoolType}`,
            type: 'GET',
            success: function(data) {
                if (data.success && data.scenario_guide.welcome_message) {
                    // 显示定制化欢迎消息
                    const welcomeHtml = `
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <h5><i class="fas fa-star mr-2"></i>专属引导已启动</h5>
                            <p>${data.scenario_guide.welcome_message}</p>
                            <button type="button" class="close" data-dismiss="alert">
                                <span>&times;</span>
                            </button>
                        </div>
                    `;

                    // 在页面顶部显示欢迎消息
                    $('body').prepend(welcomeHtml);

                    // 3秒后自动隐藏
                    setTimeout(() => {
                        $('.alert-success').fadeOut();
                    }, 3000);
                }
            }
        });
    }
}

// 检查新通知
function checkNotifications() {
    fetch('/notifications/check')
        .then(response => response.json())
        .then(data => {
            updateNotificationBadge(data.unread_count);
            updateNotificationList(data.notifications);
        })
        .catch(error => console.error('获取通知失败:', error));
}

// 更新通知图标上的未读数量
function updateNotificationBadge(count) {
    const badge = document.getElementById('notification-badge');

    if (count > 0) {
        if (badge) {
            badge.textContent = count;
        } else {
            const notificationIcon = document.getElementById('notificationDropdown');
            const newBadge = document.createElement('span');
            newBadge.id = 'notification-badge';
            newBadge.className = 'badge badge-danger notification-badge';
            newBadge.textContent = count;
            notificationIcon.appendChild(newBadge);
        }
    } else if (badge) {
        badge.remove();
    }
}

// 更新通知下拉菜单中的通知列表
function updateNotificationList(notifications) {
    const notificationList = document.getElementById('notification-list');
    if (!notificationList) return;

    if (notifications && notifications.length > 0) {
        let html = '';

        notifications.forEach(notification => {
            html += `
                <a class="dropdown-item notification-item ${notification.is_read ? '' : 'unread'}" href="/notifications/view/${notification.id}">
                    <div class="notification-title">
                        ${notification.level === 2 ? '<span class="badge badge-danger">紧急</span>' :
                          notification.level === 1 ? '<span class="badge badge-warning">重要</span>' : ''}
                        ${notification.title}
                    </div>
                    <div class="notification-content">${notification.content}</div>
                    <div class="notification-time">${notification.created_at}</div>
                </a>
            `;
        });

        notificationList.innerHTML = html;
    } else {
        notificationList.innerHTML = '<div class="dropdown-item text-center">暂无通知</div>';
    }
}
