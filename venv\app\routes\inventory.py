from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import Inventory, Warehouse, Ingredient, StorageLocation, InventoryAlert
from app import db
from datetime import datetime, date, timedelta
from sqlalchemy import func
import json

inventory_bp = Blueprint('inventory', __name__)

def safe_get_int_param(param_name, default=None):
    """安全地获取整数参数，如果转换失败返回默认值"""
    try:
        value = request.args.get(param_name)
        if value is None or value == '':
            return default
        return int(value)
    except (ValueError, TypeError):
        current_app.logger.warning(f"无效的{param_name}参数: {request.args.get(param_name)}")
        return default

@inventory_bp.route('/inventory')
@login_required
def index():
    """库存列表页面"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数，使用安全的参数获取函数
    page = safe_get_int_param('page', 1)
    per_page = current_app.config['ITEMS_PER_PAGE']
    warehouse_id = safe_get_int_param('warehouse_id')
    ingredient_id = safe_get_int_param('ingredient_id')
    status = request.args.get('status', '')
    expiry_days = safe_get_int_param('expiry_days')
    storage_location_id = safe_get_int_param('storage_location_id')
    view_type = request.args.get('view_type', 'detail')  # 'detail' 或 'summary'

    # 构建查询
    if view_type == 'detail':
        # 详细视图 - 显示每个批次的库存
        query = Inventory.query.join(Warehouse).filter(Warehouse.area_id.in_(area_ids))

        # 应用过滤条件
        if warehouse_id:
            query = query.filter(Inventory.warehouse_id == warehouse_id)
        if ingredient_id:
            query = query.filter(Inventory.ingredient_id == ingredient_id)
        if status:
            query = query.filter(Inventory.status == status)
        if expiry_days:
            expiry_date = date.today() + timedelta(days=expiry_days)
            query = query.filter(Inventory.expiry_date <= expiry_date)
        if storage_location_id:
            query = query.filter(Inventory.storage_location_id == storage_location_id)

        # 按过期日期升序排序
        query = query.order_by(Inventory.expiry_date)

        # 分页
        pagination = query.paginate(page=page, per_page=per_page, error_out=0)
        inventories = pagination.items

        # 获取仓库列表
        warehouses = Warehouse.query.filter(Warehouse.area_id.in_(area_ids)).all()

        # 获取食材列表
        ingredients = Ingredient.query.all()

        # 获取存储位置列表
        storage_locations = []
        if warehouse_id:
            storage_locations = StorageLocation.query.filter_by(warehouse_id=warehouse_id).all()

        return render_template('inventory/index.html',
                              inventories=inventories,
                              pagination=pagination,
                              warehouses=warehouses,
                              ingredients=ingredients,
                              storage_locations=storage_locations,
                              warehouse_id=warehouse_id,
                              ingredient_id=ingredient_id,
                              status=status,
                              expiry_days=expiry_days,
                              storage_location_id=storage_location_id,
                              view_type=view_type)
    else:
        # 汇总视图 - 按食材汇总库存
        # 使用 SQLAlchemy 的 func 进行分组查询
        query = db.session.query(
            Inventory.ingredient_id,
            Ingredient.name.label('ingredient_name'),
            Ingredient.category.label('ingredient_category'),
            func.sum(Inventory.quantity).label('total_quantity'),
            func.min(Inventory.unit).label('unit')
        ).join(Ingredient).join(Warehouse).filter(
            Warehouse.area_id.in_(area_ids),
            Inventory.status == '正常',
            Inventory.quantity > 0
        ).group_by(
            Inventory.ingredient_id,
            Ingredient.name,
            Ingredient.category
        )

        # 应用过滤条件
        if warehouse_id:
            query = query.filter(Inventory.warehouse_id == warehouse_id)
        if ingredient_id:
            query = query.filter(Inventory.ingredient_id == ingredient_id)
        if storage_location_id:
            query = query.filter(Inventory.storage_location_id == storage_location_id)

        # 按食材名称排序
        query = query.order_by(Ingredient.name)

        # 执行查询
        inventory_summary = query.all()

        # 获取仓库列表
        warehouses = Warehouse.query.filter(Warehouse.area_id.in_(area_ids)).all()

        # 获取食材列表
        ingredients = Ingredient.query.all()

        # 获取存储位置列表
        storage_locations = []
        if warehouse_id:
            storage_locations = StorageLocation.query.filter_by(warehouse_id=warehouse_id).all()

        return render_template('inventory/summary.html',
                              inventory_summary=inventory_summary,
                              warehouses=warehouses,
                              ingredients=ingredients,
                              storage_locations=storage_locations,
                              warehouse_id=warehouse_id,
                              ingredient_id=ingredient_id,
                              storage_location_id=storage_location_id,
                              view_type=view_type)

@inventory_bp.route('/inventory/detail/<int:id>')
@login_required
def detail(id):
    """查看库存详情"""
    inventory = Inventory.query.get_or_404(id)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(inventory.warehouse.area_id):
        flash('您没有权限查看该库存', 'danger')
        return redirect(url_for('inventory.index'))

    # 获取库存变动历史
    # TODO: 实现库存变动历史查询

    return render_template('inventory/detail.html',
                          inventory=inventory)

@inventory_bp.route('/inventory/get-storage-locations')
@login_required
def get_storage_locations():
    """获取仓库的存储位置列表（AJAX）"""
    warehouse_id = safe_get_int_param('warehouse_id')

    if not warehouse_id:
        return jsonify([])

    # 获取存储位置列表
    storage_locations = StorageLocation.query.filter_by(warehouse_id=warehouse_id, status='正常').all()

    # 转换为JSON格式
    locations = [{'id': loc.id, 'name': f"{loc.name} ({loc.location_code})"} for loc in storage_locations]

    return jsonify(locations)

@inventory_bp.route('/inventory/ingredient/<int:id>')
@login_required
def ingredient_inventory(id):
    """查看某个食材的库存情况"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取食材信息
    ingredient = Ingredient.query.get_or_404(id)

    # 获取该食材的库存列表
    inventories = Inventory.query.join(Warehouse).filter(
        Inventory.ingredient_id == id,
        Warehouse.area_id.in_(area_ids),
        Inventory.status == '正常',
        Inventory.quantity > 0
    ).order_by(Inventory.expiry_date).all()

    # 计算总库存
    total_quantity = sum(inv.quantity for inv in inventories)

    # 获取该食材的库存预警设置
    alerts = InventoryAlert.query.filter_by(ingredient_id=id).all()

    return render_template('inventory/ingredient.html',
                          ingredient=ingredient,
                          inventories=inventories,
                          total_quantity=total_quantity,
                          alerts=alerts)

@inventory_bp.route('/inventory/check-expiry')
@login_required
def check_expiry():
    """检查临期和过期库存"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取查询参数，使用安全的参数获取函数
    days = safe_get_int_param('days', 7)  # 默认7天内过期
    warehouse_id = safe_get_int_param('warehouse_id')

    # 计算临期日期
    expiry_date = date.today() + timedelta(days=days)

    # 查询临期库存
    query = Inventory.query.join(Warehouse).filter(
        Warehouse.area_id.in_(area_ids),
        Inventory.status == '正常',
        Inventory.quantity > 0,
        Inventory.expiry_date <= expiry_date
    )

    # 应用仓库过滤
    if warehouse_id:
        query = query.filter(Inventory.warehouse_id == warehouse_id)

    # 按过期日期升序排序
    query = query.order_by(Inventory.expiry_date)

    # 执行查询
    expiring_inventories = query.all()

    # 查询已过期库存
    query = Inventory.query.join(Warehouse).filter(
        Warehouse.area_id.in_(area_ids),
        Inventory.status == '正常',
        Inventory.quantity > 0,
        Inventory.expiry_date < date.today()
    )

    # 应用仓库过滤
    if warehouse_id:
        query = query.filter(Inventory.warehouse_id == warehouse_id)

    # 按过期日期升序排序
    query = query.order_by(Inventory.expiry_date)

    # 执行查询
    expired_inventories = query.all()

    # 获取仓库列表
    warehouses = Warehouse.query.filter(Warehouse.area_id.in_(area_ids)).all()

    return render_template('inventory/expiry.html',
                          expiring_inventories=expiring_inventories,
                          expired_inventories=expired_inventories,
                          days=days,
                          warehouse_id=warehouse_id,
                          warehouses=warehouses)
