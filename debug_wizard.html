<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>入库向导调试页面</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .debug-info { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { color: red; }
        .success { color: green; }
        .warning { color: orange; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>入库向导调试页面</h1>
        
        <div class="debug-info">
            <h3>调试信息</h3>
            <div id="debugLog"></div>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>从采购计划创建</h5>
                    </div>
                    <div class="card-body text-center">
                        <i class="fas fa-file-import fa-3x mb-3"></i>
                        <p>自动导入采购计划中的食材信息，快速完成入库</p>
                        <button type="button" class="btn btn-primary" id="importFromPurchaseBtn">
                            选择采购计划
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 采购计划选择模态框 -->
        <div class="modal fade" id="purchaseOrderModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">选择采购计划</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>订单信息</th>
                                    <th>创建时间</th>
                                    <th>供应商</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="purchaseOrdersTable">
                                <!-- 动态加载内容 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            $('#debugLog').append(`<div class="${className}">[${timestamp}] ${message}</div>`);
            console.log(`[${timestamp}] ${message}`);
        }

        $(document).ready(function() {
            log('页面加载完成');
            
            // 从采购计划导入食材按钮点击事件
            $('#importFromPurchaseBtn').on('click', function() {
                log('点击了从采购计划导入食材按钮');
                loadPurchaseOrders();
                $('#purchaseOrderModal').modal('show');
            });
        });

        // 加载采购计划列表
        function loadPurchaseOrders() {
            log('开始加载采购计划列表');
            
            // 显示加载提示
            $('#purchaseOrdersTable').html(`
                <tr>
                    <td colspan="5" class="text-center">
                        <i class="fas fa-spinner fa-spin"></i> 正在加载采购计划...
                    </td>
                </tr>
            `);

            // 发送AJAX请求获取采购计划列表
            $.ajax({
                url: '/api/purchase-orders/available',
                type: 'GET',
                dataType: 'json',
                timeout: 10000, // 10秒超时
                beforeSend: function() {
                    log('发送AJAX请求到: /api/purchase-orders/available');
                },
                success: function(response) {
                    log('API响应成功: ' + JSON.stringify(response), 'success');
                    if (response.success && response.data && response.data.length > 0) {
                        renderPurchaseOrdersList(response.data);
                    } else {
                        showNoPurchaseOrdersMessage();
                    }
                },
                error: function(xhr, status, error) {
                    log(`API请求失败: ${status} - ${error}`, 'error');
                    log(`响应状态码: ${xhr.status}`, 'error');
                    log(`响应内容: ${xhr.responseText}`, 'error');
                    
                    if (status === 'timeout') {
                        showErrorMessage('请求超时，请检查网络连接后重试');
                    } else {
                        showErrorMessage('加载采购计划失败，请稍后重试');
                    }
                }
            });
        }

        // 渲染采购计划列表
        function renderPurchaseOrdersList(orders) {
            log(`渲染 ${orders.length} 个采购计划`);
            let html = '';
            orders.forEach(order => {
                html += `
                    <tr>
                        <td>
                            <strong>${order.order_number}</strong>
                            <br>
                            <small class="text-muted">${order.status}</small>
                        </td>
                        <td>
                            <small>${order.created_at}</small>
                        </td>
                        <td>
                            ${order.supplier_name || '自购'}
                        </td>
                        <td>
                            <span class="badge bg-success">${order.status}</span>
                        </td>
                        <td>
                            <button type="button" class="btn btn-primary btn-sm import-purchase-btn"
                                    data-order-id="${order.id}"
                                    data-order-number="${order.order_number}">
                                导入
                            </button>
                        </td>
                    </tr>
                `;
            });
            $('#purchaseOrdersTable').html(html);
        }

        // 显示无采购计划消息
        function showNoPurchaseOrdersMessage() {
            log('显示无采购计划消息', 'warning');
            $('#purchaseOrdersTable').html(`
                <tr>
                    <td colspan="5" class="text-center py-4">
                        <h5 class="text-muted">暂无可用的采购计划</h5>
                        <p class="text-muted">当前学校没有状态为"已确认"且未入库的采购计划</p>
                        <button type="button" class="btn btn-outline-secondary" onclick="loadPurchaseOrders()">
                            重新加载
                        </button>
                    </td>
                </tr>
            `);
        }

        // 显示错误消息
        function showErrorMessage(message) {
            log(`显示错误消息: ${message}`, 'error');
            $('#purchaseOrdersTable').html(`
                <tr>
                    <td colspan="5" class="text-center py-4">
                        <h5 class="text-warning">加载失败</h5>
                        <p class="text-muted">${message}</p>
                        <button type="button" class="btn btn-outline-primary" onclick="loadPurchaseOrders()">
                            重新加载
                        </button>
                    </td>
                </tr>
            `);
        }
    </script>
</body>
</html>
