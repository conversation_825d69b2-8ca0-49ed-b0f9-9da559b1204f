<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>采购计划API测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .response { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; white-space: pre-wrap; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <div class="container">
        <h1>采购计划API测试</h1>
        
        <div class="test-section">
            <h3>测试 /api/purchase-orders/available 接口</h3>
            <button onclick="testAvailableOrders()">获取可用采购计划</button>
            <div id="availableOrdersResponse" class="response"></div>
        </div>
        
        <div class="test-section">
            <h3>测试导入功能</h3>
            <input type="number" id="orderIdInput" placeholder="输入采购订单ID" style="padding: 8px; margin: 5px;">
            <button onclick="testImportOrder()">测试导入采购计划</button>
            <div id="importResponse" class="response"></div>
        </div>
    </div>

    <script>
        function testAvailableOrders() {
            $('#availableOrdersResponse').text('正在请求...').removeClass('error success');
            
            $.ajax({
                url: '/api/purchase-orders/available',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    $('#availableOrdersResponse')
                        .addClass('success')
                        .text(JSON.stringify(response, null, 2));
                },
                error: function(xhr, status, error) {
                    $('#availableOrdersResponse')
                        .addClass('error')
                        .text(`请求失败: ${xhr.status} ${xhr.statusText}\n${xhr.responseText}`);
                }
            });
        }
        
        function testImportOrder() {
            const orderId = $('#orderIdInput').val();
            if (!orderId) {
                alert('请输入采购订单ID');
                return;
            }
            
            $('#importResponse').text('正在导入...').removeClass('error success');
            
            $.ajax({
                url: `/stock-in/import-from-purchase/${orderId}`,
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    $('#importResponse')
                        .addClass('success')
                        .text(JSON.stringify(response, null, 2));
                },
                error: function(xhr, status, error) {
                    $('#importResponse')
                        .addClass('error')
                        .text(`导入失败: ${xhr.status} ${xhr.statusText}\n${xhr.responseText}`);
                }
            });
        }
        
        // 页面加载时自动测试API
        $(document).ready(function() {
            console.log('页面加载完成，可以开始测试API');
        });
    </script>
</body>
</html>
