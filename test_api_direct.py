#!/usr/bin/env python3
"""
直接测试采购订单API
"""

import sys
import os
import requests
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_api():
    """测试API"""
    base_url = "http://127.0.0.1:5000"
    
    # 首先登录获取session
    session = requests.Session()
    
    print("=== 测试采购订单API ===")
    
    # 测试登录
    login_data = {
        'username': 'admin',  # 请根据实际情况修改
        'password': 'admin123'  # 请根据实际情况修改
    }
    
    try:
        # 获取登录页面（获取CSRF token）
        login_page = session.get(f"{base_url}/auth/login")
        print(f"获取登录页面: {login_page.status_code}")
        
        # 尝试登录
        login_response = session.post(f"{base_url}/auth/login", data=login_data)
        print(f"登录响应: {login_response.status_code}")
        
        if login_response.status_code == 200 and 'dashboard' in login_response.url:
            print("✓ 登录成功")
        else:
            print("✗ 登录失败，尝试直接访问API")
        
        # 测试API
        api_url = f"{base_url}/api/purchase-orders/available"
        print(f"\n测试API: {api_url}")
        
        response = session.get(api_url)
        print(f"API响应状态码: {response.status_code}")
        print(f"API响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("✓ API响应成功")
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if data.get('success') and data.get('data'):
                    print(f"\n找到 {len(data['data'])} 个可用采购订单:")
                    for order in data['data']:
                        print(f"  - 订单号: {order['order_number']} (ID: {order['id']})")
                        print(f"    状态: {order['status']}")
                        print(f"    供应商: {order['supplier_name']}")
                        print(f"    创建时间: {order['created_at']}")
                        print()
                else:
                    print("API返回成功但没有数据")
                    
            except json.JSONDecodeError as e:
                print(f"✗ JSON解析失败: {e}")
                print(f"原始响应: {response.text}")
        else:
            print(f"✗ API请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("✗ 连接失败，请确保Flask应用正在运行")
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")

def test_database_direct():
    """直接测试数据库查询"""
    print("\n=== 直接测试数据库查询 ===")
    
    try:
        from app import create_app, db
        from app.models import PurchaseOrder, StockIn, AdministrativeArea
        
        app = create_app()
        
        with app.app_context():
            # 获取所有区域
            areas = AdministrativeArea.query.all()
            print(f"找到 {len(areas)} 个区域:")
            for area in areas:
                print(f"  - {area.name} (ID: {area.id})")
            
            if not areas:
                print("没有找到区域数据")
                return
            
            # 对每个区域查询采购订单
            for area in areas:
                print(f"\n检查区域 {area.name} (ID: {area.id}) 的采购订单:")
                
                # 查询该区域的采购订单
                orders = PurchaseOrder.query.filter(
                    PurchaseOrder.area_id == area.id,
                    PurchaseOrder.status.in_(['已确认', '准备入库'])
                ).order_by(PurchaseOrder.order_date.desc()).all()
                
                print(f"  找到 {len(orders)} 个符合条件的采购订单:")
                
                for order in orders:
                    # 检查是否已经创建了入库单
                    existing_stock_in = StockIn.query.filter_by(purchase_order_id=order.id).first()
                    has_stock_in = "是" if existing_stock_in else "否"
                    
                    supplier_name = order.supplier.name if order.supplier else "自购"
                    
                    print(f"    - 订单号: {order.order_number} (ID: {order.id})")
                    print(f"      状态: {order.status}")
                    print(f"      供应商: {supplier_name}")
                    print(f"      创建时间: {order.created_at}")
                    print(f"      已创建入库单: {has_stock_in}")
                    
                    if not existing_stock_in:
                        print(f"      ✓ 可用于入库")
                    else:
                        print(f"      ✗ 已创建入库单: {existing_stock_in.stock_in_number}")
                    print()
                    
    except Exception as e:
        print(f"✗ 数据库查询失败: {str(e)}")

if __name__ == '__main__':
    test_database_direct()
    test_api()
