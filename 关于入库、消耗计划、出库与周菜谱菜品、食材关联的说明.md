生成食材清单失败: 获取食材清单失败# 入库、消耗计划、出库与周菜谱菜品、食材关联说明

## 📋 **系统概述**

本系统实现了完整的食材从入库到消耗的全流程管理，通过学校级数据隔离确保各学校数据安全，并建立了完整的食材溯源体系。

## 🏫 **学校级数据绑定架构**

### **核心绑定关系**
```
学校(administrative_areas)
    ↓
仓库(warehouses.area_id)
    ↓
入库(stock_ins.warehouse_id) → 库存(inventories.warehouse_id) → 出库(stock_outs.warehouse_id)
    ↓
消耗计划(consumption_plans.area_id) ← 周菜单(weekly_menus.area_id)
```

### **数据隔离策略**
- **严格的学校级隔离**：所有操作都基于用户可访问的学校区域
- **权限验证**：通过 `warehouse.area_id` 验证用户操作权限
- **查询过滤**：所有列表查询都添加学校区域过滤条件

## 🔄 **业务流程关联**

### **1. 入库流程**http://127.0.0.1:5000/stock-in/wizard
```
采购订单 → 供应商配送 → 入库单(stock_ins) → 入库明细(stock_in_items) → 库存(inventories)
```

**关键字段**：
- `stock_ins.warehouse_id` → `warehouses.area_id` （学校绑定）
- `stock_in_items.batch_number` （批次溯源）
- `stock_in_items.supplier_id` （供应商溯源）

### **2. 消耗计划流程**
```
周菜单(weekly_menus) → 消耗计划(consumption_plans) → 消耗明细(consumption_details)
```

**关键字段**：
- `weekly_menus.area_id` （学校绑定）
- `consumption_plans.area_id` （学校绑定）
- `consumption_plans.consumption_date` + `meal_type` （日期餐次）

### **3. 出库流程**
```
消耗计划 → 出库单(stock_outs) → 出库明细(stock_out_items) → 库存更新
```

**关键字段**：
- `stock_outs.warehouse_id` → `warehouses.area_id` （学校绑定）
- `stock_outs.consumption_plan_id` （消耗计划关联）
- `stock_out_items.batch_number` （批次溯源）

## 🍽️ **周菜谱与食材关联**

### **菜谱数据结构**
```
周菜单(weekly_menus)
    ↓
周菜单食谱(weekly_menu_recipes) → 食谱(recipes)
    ↓
食谱食材(recipe_ingredients) → 食材(ingredients)
```

### **关联查询逻辑**
1. **通过消耗计划获取信息**：
   - 消耗日期：`consumption_plans.consumption_date`
   - 餐次：`consumption_plans.meal_type`
   - 学校：`consumption_plans.area_id` 或 `warehouses.area_id`

2. **查找对应周菜单**：
   ```sql
   SELECT * FROM weekly_menus
   WHERE week_start <= consumption_date
   AND week_end >= consumption_date
   AND area_id = school_area_id
   AND status = '已发布'
   ```

3. **获取当日餐次食谱**：
   ```sql
   SELECT * FROM weekly_menu_recipes
   WHERE weekly_menu_id = menu_id
   AND day_of_week = weekday
   AND meal_type = meal_type
   ```

4. **提取食谱食材**：
   ```sql
   SELECT ingredients.* FROM recipe_ingredients
   JOIN ingredients ON recipe_ingredients.ingredient_id = ingredients.id
   WHERE recipe_ingredients.recipe_id IN (recipe_ids)
   ```

## 🔍 **食材溯源体系**

### **正向溯源（从入库到消耗）**
```
供应商 → 入库批次 → 库存 → 出库 → 消耗计划 → 具体菜谱
```

### **反向溯源（从菜谱到供应商）**
```
菜谱 → 食材 → 出库批次 → 库存 → 入库批次 → 供应商
```

### **溯源关键信息**
- **批次号**：`batch_number` 贯穿整个流程
- **供应商信息**：从入库单或溯源批次获取
- **质量证明**：检验检疫证明文档
- **消耗去向**：具体日期、餐次、菜谱

## 📊 **数据关联示例**

### **朝阳区实验中学 2025-05-28 午餐**

**1. 学校信息**：
- 学校：朝阳区实验中学 (area_id: 42)
- 仓库：学校食堂仓库 (warehouse_id: 1, area_id: 42)

**2. 消耗计划**：
- 日期：2025-05-28 (周三)
- 餐次：午餐
- 计划ID：consumption_plan_id: 15

**3. 周菜单查询**：
- 查询条件：`day_of_week = 3`, `meal_type = '午餐'`, `area_id = 42`
- 结果：5个食谱（红烧肉、香菇肉、鸡肉牛排汤、黄花鸡丁、鸡翅拌）

**4. 出库溯源**：
- 出库单：stock_out_id: 7
- 出库食材：通过批次号关联到具体菜谱
- 供应商信息：从入库记录获取

## ⚙️ **技术实现要点**

### **1. 学校区域ID获取优先级**
```python
# 优先级：消耗计划 > 菜单计划 > 仓库
if consumption_plan.area_id:
    area_id = consumption_plan.area_id
elif menu_plan.area_id:
    area_id = menu_plan.area_id
else:
    area_id = warehouse.area_id
```

### **2. 调味品过滤**
```python
# 关键词过滤
seasoning_keywords = ['盐', '糖', '醋', '酱油', '料酒', '胡椒', '花椒', ...]

# 分类过滤
if '调味品' in category_name or '香料' in category_name:
    is_seasoning = True
```

### **3. 权限验证**
```python
# 检查用户是否有权限访问该学校数据
if not current_user.can_access_area_by_id(warehouse.area_id):
    flash('您没有权限操作该数据', 'danger')
```

## 🎯 **系统优势**

1. **数据安全**：严格的学校级数据隔离
2. **完整溯源**：从供应商到餐桌的全链路追踪
3. **业务闭环**：入库-库存-出库-消耗的完整流程
4. **智能关联**：自动关联菜谱与食材信息
5. **权限控制**：基于学校的精细化权限管理

## 📝 **注意事项**

1. **数据库字段**：部分表没有直接的 `area_id` 字段，通过仓库关联获取学校信息
2. **时间处理**：使用 DATETIME2 类型和原始SQL避免ORM时间戳问题
3. **异常处理**：对象属性访问需要进行类型检查和安全验证
4. **性能优化**：合理使用JOIN查询减少数据库访问次数

这个系统确保了食材管理的完整性、安全性和可追溯性，为学校食品安全管理提供了强有力的技术支撑。

## 📋 **数据库表结构详情**

### **核心表结构**

#### **1. 学校区域表 (administrative_areas)**
```sql
- id: 主键
- name: 学校名称
- level: 区域级别
- parent_id: 父级区域
```

#### **2. 仓库表 (warehouses)**
```sql
- id: 主键
- name: 仓库名称
- area_id: 学校区域ID (外键)
- location: 仓库位置
- manager_id: 管理员ID
- status: 状态 (正常/维护中/已关闭)
```

#### **3. 入库表 (stock_ins)**
```sql
- id: 主键
- stock_in_number: 入库单号
- warehouse_id: 仓库ID (外键)
- delivery_id: 配送单ID (外键)
- purchase_order_id: 采购订单ID (外键)
- stock_in_date: 入库日期
- stock_in_type: 入库类型 (采购入库/调拨入库/退货入库)
- operator_id: 操作员ID
- inspector_id: 检验员ID
- status: 状态 (待审核/已审核/已入库/已取消)
```

#### **4. 入库明细表 (stock_in_items)**
```sql
- id: 主键
- stock_in_id: 入库单ID (外键)
- ingredient_id: 食材ID (外键)
- batch_number: 批次号 (重要：溯源关键字段)
- quantity: 数量
- unit: 单位
- unit_price: 单价
- production_date: 生产日期
- expiry_date: 过期日期
- storage_location_id: 存储位置ID
- supplier_id: 供应商ID (外键)
- quality_check_result: 质检结果 (合格/不合格)
```

#### **5. 库存表 (inventories)**
```sql
- id: 主键
- warehouse_id: 仓库ID (外键)
- storage_location_id: 存储位置ID (外键)
- ingredient_id: 食材ID (外键)
- batch_number: 批次号 (重要：溯源关键字段)
- quantity: 库存数量
- unit: 单位
- production_date: 生产日期
- expiry_date: 过期日期
- supplier_id: 供应商ID (外键)
- status: 状态 (正常/待检/冻结/已过期)
```

#### **6. 出库表 (stock_outs)**
```sql
- id: 主键
- stock_out_number: 出库单号
- warehouse_id: 仓库ID (外键)
- consumption_plan_id: 消耗计划ID (外键)
- stock_out_date: 出库日期
- stock_out_type: 出库类型 (消耗出库/调拨出库/报废出库)
- recipient: 领用人
- department: 领用部门
- operator_id: 操作员ID
- approver_id: 审核员ID
- status: 状态 (待审核/已审核/已出库/已取消)
```

#### **7. 出库明细表 (stock_out_items)**
```sql
- id: 主键
- stock_out_id: 出库单ID (外键)
- inventory_id: 库存ID (外键)
- ingredient_id: 食材ID (外键)
- batch_number: 批次号 (重要：溯源关键字段)
- quantity: 出库数量
- unit: 单位
- consumption_detail_id: 消耗明细ID (外键)
```

#### **8. 消耗计划表 (consumption_plans)**
```sql
- id: 主键
- menu_plan_id: 菜单计划ID (外键)
- area_id: 学校区域ID (外键)
- consumption_date: 消耗日期 (重要：关联周菜单)
- meal_type: 餐次 (早餐/午餐/晚餐/加餐) (重要：关联周菜单)
- diners_count: 用餐人数
- status: 状态 (计划中/已审核/已执行/已取消)
- created_by: 创建人ID
- approved_by: 审核人ID
```

#### **9. 周菜单表 (weekly_menus)**
```sql
- id: 主键
- area_id: 学校区域ID (外键)
- week_start: 周开始日期
- week_end: 周结束日期
- status: 状态 (草稿/已发布/已归档)
- created_by: 创建人ID
```

#### **10. 周菜单食谱表 (weekly_menu_recipes)**
```sql
- id: 主键
- weekly_menu_id: 周菜单ID (外键)
- recipe_id: 食谱ID (外键)
- day_of_week: 星期几 (1-7，1表示周一) (重要：关联消耗计划)
- meal_type: 餐次 (早餐/午餐/晚餐) (重要：关联消耗计划)
```

#### **11. 食谱表 (recipes)**
```sql
- id: 主键
- name: 食谱名称
- category: 食谱分类
- description: 描述
- main_image: 主图片
- cooking_time: 烹饪时间
- difficulty: 难度等级
```

#### **12. 食谱食材表 (recipe_ingredients)**
```sql
- id: 主键
- recipe_id: 食谱ID (外键)
- ingredient_id: 食材ID (外键)
- quantity: 用量
- unit: 单位
- notes: 备注
```

#### **13. 食材表 (ingredients)**
```sql
- id: 主键
- name: 食材名称 (重要：溯源匹配关键字段)
- category: 食材分类 (用于过滤调味品)
- unit: 基本单位
- shelf_life: 保质期
- storage_conditions: 存储条件
```

## 🔗 **关键关联关系**

### **学校数据隔离关联**
```sql
-- 通过仓库获取学校信息
stock_ins.warehouse_id → warehouses.area_id
stock_outs.warehouse_id → warehouses.area_id
inventories.warehouse_id → warehouses.area_id

-- 直接学校关联
consumption_plans.area_id
weekly_menus.area_id
```

### **溯源关联**
```sql
-- 批次号溯源链
stock_in_items.batch_number = inventories.batch_number = stock_out_items.batch_number

-- 供应商溯源
stock_in_items.supplier_id → suppliers.id
inventories.supplier_id → suppliers.id
```

### **菜谱食材关联**
```sql
-- 周菜单到食谱
weekly_menus.id → weekly_menu_recipes.weekly_menu_id
weekly_menu_recipes.recipe_id → recipes.id

-- 食谱到食材
recipes.id → recipe_ingredients.recipe_id
recipe_ingredients.ingredient_id → ingredients.id

-- 消耗计划关联
consumption_plans.consumption_date + meal_type → weekly_menu_recipes.day_of_week + meal_type
```

## 🔧 **核心查询逻辑**

### **1. 获取出库单关联的菜谱信息**
```python
def get_recipes_by_stock_out(stock_out_id):
    # 1. 获取出库单信息
    stock_out = StockOut.query.get(stock_out_id)

    # 2. 获取学校区域ID
    area_id = stock_out.warehouse.area_id

    # 3. 通过消耗计划获取日期和餐次
    consumption_plan = stock_out.consumption_plan
    consumption_date = consumption_plan.consumption_date
    meal_type = consumption_plan.meal_type

    # 4. 计算星期几
    weekday = consumption_date.weekday()  # 0-6
    day_of_week = weekday + 1  # 1-7

    # 5. 查找周菜单
    weekly_menu = WeeklyMenu.query.filter(
        WeeklyMenu.week_start <= consumption_date,
        WeeklyMenu.week_end >= consumption_date,
        WeeklyMenu.area_id == area_id,
        WeeklyMenu.status == '已发布'
    ).first()

    # 6. 获取当日餐次的食谱
    if weekly_menu:
        weekly_recipes = WeeklyMenuRecipe.query.filter(
            WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
            WeeklyMenuRecipe.day_of_week == day_of_week,
            WeeklyMenuRecipe.meal_type == meal_type
        ).all()

        recipes = [wr.recipe for wr in weekly_recipes if wr.recipe]
        return recipes

    return []
```

### **2. 食材溯源查询**
```python
def get_ingredient_traceability(batch_number):
    # 1. 查找入库信息
    stock_in_items = StockInItem.query.filter_by(batch_number=batch_number).all()

    # 2. 获取供应商信息
    suppliers = []
    for item in stock_in_items:
        if item.supplier:
            suppliers.append(item.supplier)

    # 3. 查找出库信息
    stock_out_items = StockOutItem.query.filter_by(batch_number=batch_number).all()

    # 4. 关联消耗计划和菜谱
    consumption_info = []
    for out_item in stock_out_items:
        if out_item.stock_out.consumption_plan:
            plan = out_item.stock_out.consumption_plan
            # 获取该日期餐次的菜谱
            recipes = get_recipes_by_consumption_plan(plan)
            consumption_info.append({
                'date': plan.consumption_date,
                'meal_type': plan.meal_type,
                'recipes': recipes
            })

    return {
        'batch_number': batch_number,
        'suppliers': suppliers,
        'consumption_info': consumption_info
    }
```

### **3. 学校数据权限过滤**
```python
def get_user_accessible_stock_outs(user):
    # 获取用户可访问的学校区域
    accessible_areas = user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 通过仓库的area_id进行过滤
    stock_outs = StockOut.query.join(Warehouse).filter(
        Warehouse.area_id.in_(area_ids)
    ).all()

    return stock_outs
```

## 🎯 **系统特色功能**

### **1. 智能调味品过滤**
- 通过关键词匹配过滤调味品
- 通过食材分类过滤调味品
- 只显示主要食材，提高溯源信息的实用性

### **2. 多级学校区域ID获取**
- 优先使用消耗计划的area_id
- 其次使用菜单计划的area_id
- 最后使用仓库的area_id
- 确保在任何情况下都能获取到正确的学校信息

### **3. 完整的权限验证体系**
- 列表查询：通过JOIN过滤只显示用户可访问的数据
- 详情查看：验证用户是否有权限查看特定记录
- 操作权限：验证用户是否有权限进行增删改操作

### **4. 灵活的时间处理**
- 使用DATETIME2类型避免精度问题
- 使用原始SQL避免ORM时间戳处理问题
- 支持跨周的菜单查询

这个完整的文档为系统的维护和扩展提供了详细的技术参考。
