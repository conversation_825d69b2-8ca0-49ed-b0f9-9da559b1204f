from flask import Blueprint, render_template, request, jsonify, current_app, flash, redirect, url_for
from flask_login import login_required, current_user
from datetime import datetime, timedelta
from sqlalchemy import text
import uuid
import os
from werkzeug.utils import secure_filename

from app import db
from app.models import StockIn, StockInItem, StockInDocument, IngredientInspection
from app.models import Warehouse, StorageLocation, Supplier, Ingredient, PurchaseOrder, PurchaseOrderItem, Inventory, SupplierSchoolRelation
from app.utils.school_required import school_required

# 创建蓝图
stock_in_wizard_bp = Blueprint('stock_in_wizard', __name__)

def get_purchase_order_menu_info(purchase_order):
    """获取采购订单对应的菜谱信息"""
    try:
        # 检查采购订单是否有备注信息包含日期和餐次
        if purchase_order.notes:
            # 尝试从备注中解析菜谱信息
            # 备注格式可能是："2024-01-15 午餐、晚餐的菜谱采购"
            import re
            date_pattern = r'(\d{4}-\d{2}-\d{2})'
            meal_pattern = r'(早餐|午餐|晚餐)'

            dates = re.findall(date_pattern, purchase_order.notes)
            meals = re.findall(meal_pattern, purchase_order.notes)

            if dates and meals:
                return {
                    'dates': dates,
                    'meals': meals,
                    'description': f"{dates[0]} {', '.join(meals)}"
                }

        # 如果没有备注信息，尝试通过创建时间推断
        order_date = purchase_order.created_at.date()
        return {
            'dates': [order_date.strftime('%Y-%m-%d')],
            'meals': ['午餐'],  # 默认午餐
            'description': f"{order_date.strftime('%Y-%m-%d')} 午餐（推断）"
        }

    except Exception as e:
        # 如果解析失败，返回默认信息
        return {
            'dates': [],
            'meals': [],
            'description': '菜谱信息不明确'
        }

@stock_in_wizard_bp.route('/stock-in/wizard')
@login_required
@school_required
def wizard(user_area):
    """入库向导页面"""
    # user_area 参数由 @school_required 装饰器自动传入

    # 首先检查学校是否有可用仓库
    school_warehouses = Warehouse.query.filter(
        Warehouse.area_id == user_area.id,
        Warehouse.status == '正常'
    ).all()

    if not school_warehouses:
        flash(f'学校 {user_area.name} 还没有设置仓库，请先创建仓库后再进行入库操作', 'warning')
        return redirect(url_for('warehouse.create', area_id=user_area.id))

    # 获取当前学校的默认仓库（第一个仓库）
    default_warehouse = school_warehouses[0]

    # 获取当前学校的存储位置列表
    storage_locations = StorageLocation.query.join(Warehouse).filter(
        Warehouse.area_id == user_area.id,
        StorageLocation.status == '正常'
    ).all()

    # 获取当前学校的供应商列表
    suppliers = Supplier.query.join(SupplierSchoolRelation).filter(
        SupplierSchoolRelation.area_id == user_area.id,
        Supplier.status == 1,
        SupplierSchoolRelation.status == 1
    ).distinct().all()

    # 获取准备入库的采购订单（已确认状态）
    pending_purchase_orders = PurchaseOrder.query.filter(
        PurchaseOrder.area_id == user_area.id,
        PurchaseOrder.status == '已确认'
    ).order_by(PurchaseOrder.created_at.desc()).all()

    # 为每个采购订单添加菜谱信息
    for order in pending_purchase_orders:
        order.menu_info = get_purchase_order_menu_info(order)

    # 获取当前日期
    today = datetime.now().strftime('%Y-%m-%d')

    return render_template('stock_in/wizard.html',
                          user_area=user_area,
                          default_warehouse=default_warehouse,
                          storage_locations=storage_locations,
                          suppliers=suppliers,
                          pending_purchase_orders=pending_purchase_orders,
                          today=today)

@stock_in_wizard_bp.route('/stock-in/wizard/<int:id>')
@login_required
def wizard_edit(id):
    """编辑入库单向导页面"""
    # 获取入库单
    stock_in = StockIn.query.get_or_404(id)

    # 检查权限
    if stock_in.status != '待审核':
        flash('只有待审核状态的入库单可以编辑', 'danger')
        return redirect(url_for('stock_in.index'))

    # 获取仓库列表
    warehouses = Warehouse.query.all()

    # 获取存储位置列表
    storage_locations = StorageLocation.query.all()

    # 获取供应商列表
    suppliers = Supplier.query.all()

    # 获取当前日期
    today = datetime.now().strftime('%Y-%m-%d')

    return render_template('stock_in/wizard.html',
                          stock_in=stock_in,
                          warehouses=warehouses,
                          storage_locations=storage_locations,
                          suppliers=suppliers,
                          today=today)

@stock_in_wizard_bp.route('/stock-in/create', methods=['POST'])
@login_required
@school_required
def create_stock_in(user_area):
    """创建入库单"""
    try:
        data = request.json

        # 获取当前学校的默认仓库
        default_warehouse = Warehouse.query.filter(
            Warehouse.area_id == user_area.id,
            Warehouse.status == '正常'
        ).first()

        if not default_warehouse:
            return jsonify({
                'success': False,
                'message': f'学校 {user_area.name} 还没有设置仓库，请先创建仓库'
            })

        # 生成入库单号
        stock_in_number = f"RK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 使用原始SQL语句创建入库单，避免ORM处理datetime字段
        sql = text("""
        INSERT INTO stock_ins (stock_in_number, warehouse_id, stock_in_type, stock_in_date, operator_id, status, notes)
        OUTPUT inserted.id
        VALUES (:stock_in_number, :warehouse_id, :stock_in_type, :stock_in_date, :operator_id, :status, :notes)
        """)

        # 执行SQL语句
        result = db.session.execute(sql, {
            'stock_in_number': stock_in_number,
            'warehouse_id': default_warehouse.id,  # 使用默认仓库
            'stock_in_type': data['stock_in_type'],
            'stock_in_date': data['stock_in_date'],  # 直接使用字符串格式的日期
            'operator_id': current_user.id,
            'status': '待审核',
            'notes': data.get('notes', '')
        })

        # 获取新创建的入库单ID
        stock_in_id = result.fetchone()[0]

        # 提交事务
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '入库单创建成功',
            'stock_in_id': stock_in_id
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建入库单时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'创建入库单时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/create-from-purchase', methods=['POST'])
@login_required
@school_required
def create_from_purchase(user_area):
    """从采购订单创建入库单"""
    try:
        data = request.json
        current_app.logger.info(f"从采购订单创建入库单，数据: {data}")

        # 验证必要参数
        if not data.get('purchase_order_id'):
            return jsonify({
                'success': False,
                'message': '缺少采购订单ID'
            })

        # 获取采购订单
        purchase_order = PurchaseOrder.query.get(data['purchase_order_id'])
        if not purchase_order:
            return jsonify({
                'success': False,
                'message': '采购订单不存在'
            })

        # 检查权限
        if purchase_order.area_id != user_area.id:
            return jsonify({
                'success': False,
                'message': '您没有权限操作该采购订单'
            })

        # 获取默认仓库
        default_warehouse = Warehouse.query.filter(
            Warehouse.area_id == user_area.id,
            Warehouse.status == '正常'
        ).first()

        if not default_warehouse:
            return jsonify({
                'success': False,
                'message': f'学校 {user_area.name} 还没有设置仓库，请先创建仓库'
            })

        # 生成入库单号
        stock_in_number = f"RK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 使用原始SQL创建入库单
        sql = text("""
        INSERT INTO stock_ins (stock_in_number, warehouse_id, stock_in_type, stock_in_date, operator_id, purchase_order_id, status, notes)
        OUTPUT inserted.id
        VALUES (:stock_in_number, :warehouse_id, :stock_in_type, :stock_in_date, :operator_id, :purchase_order_id, :status, :notes)
        """)

        result = db.session.execute(sql, {
            'stock_in_number': stock_in_number,
            'warehouse_id': default_warehouse.id,
            'stock_in_type': data.get('stock_in_type', '采购入库'),
            'stock_in_date': data.get('stock_in_date', datetime.now().strftime('%Y-%m-%d')),
            'operator_id': current_user.id,
            'purchase_order_id': purchase_order.id,
            'status': '待审核',
            'notes': data.get('notes', f'从采购订单 {purchase_order.order_number} 创建')
        })

        stock_in_id = result.fetchone()[0]
        db.session.commit()

        current_app.logger.info(f"成功创建入库单，ID: {stock_in_id}")

        return jsonify({
            'success': True,
            'message': '入库单创建成功',
            'stock_in_id': stock_in_id
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"从采购订单创建入库单时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'从采购订单创建入库单时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/create-from-purchase/<int:purchase_order_id>')
@login_required
def create_from_purchase_get(purchase_order_id):
    """从采购订单创建入库单（GET请求）"""
    try:
        # 获取采购订单信息
        purchase_order = PurchaseOrder.query.get_or_404(purchase_order_id)

        # 检查用户是否有权限操作该采购订单
        if not current_user.can_access_area_by_id(purchase_order.area_id):
            flash('您没有权限操作该区域的采购订单', 'danger')
            return redirect(url_for('purchase_order.index'))

        # 生成入库单号
        stock_in_number = f"RK{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 根据采购订单的区域获取相应的仓库
        warehouses = Warehouse.query.filter_by(area_id=purchase_order.area_id, status='正常').all()

        if not warehouses:
            area = purchase_order.area
            flash(f'学校 {area.name} 还没有设置仓库，请先创建仓库后再进行入库操作', 'warning')
            return redirect(url_for('warehouse.create', area_id=purchase_order.area_id))

        # 如果只有一个仓库，直接使用；如果有多个，使用第一个（后续可以优化为选择界面）
        default_warehouse = warehouses[0]

        # 创建入库单
        stock_in = StockIn(
            stock_in_number=stock_in_number,
            warehouse_id=default_warehouse.id if default_warehouse else None,
            stock_in_type='采购入库',
            stock_in_date=datetime.now().date(),
            operator_id=current_user.id,
            purchase_order_id=purchase_order.id,
            status='待审核',
            notes=f'从采购订单 {purchase_order.order_number} 创建'
        )

        db.session.add(stock_in)
        db.session.commit()

        # 从采购订单导入食材
        items = PurchaseOrderItem.query.filter_by(order_id=purchase_order.id).all()

        for item in items:
            # 检查该食材是否需要检验检疫
            needs_inspection = False
            if item.ingredient.category_rel:
                needs_inspection = item.ingredient.category_rel.needs_inspection

            # 计算默认的生产日期和过期日期
            production_date = datetime.now().date()
            # 默认保质期为30天，如果食材有保质期信息则使用食材的保质期
            shelf_life = item.ingredient.shelf_life or 30
            expiry_date = production_date + timedelta(days=shelf_life)

            # 生成批次号
            batch_number = f"B{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:6]}"

            # 创建入库明细
            stock_in_item = StockInItem(
                stock_in_id=stock_in.id,
                ingredient_id=item.ingredient_id,
                purchase_order_item_id=item.id,
                batch_number=batch_number,
                quantity=item.quantity,
                unit=item.unit,
                production_date=production_date,
                expiry_date=expiry_date,
                unit_price=item.unit_price,
                supplier_id=purchase_order.supplier_id
            )

            db.session.add(stock_in_item)

        db.session.commit()

        # 自动审核入库单
        stock_in.status = '已审核'
        stock_in.inspector_id = current_user.id
        db.session.commit()

        # 自动执行入库（更新库存）
        try:
            # 获取入库明细
            stock_in_items = StockInItem.query.filter_by(stock_in_id=stock_in.id).all()

            # 更新库存
            for item in stock_in_items:
                # 检查是否已存在相同批次的库存
                existing_inventory = Inventory.query.filter_by(
                    warehouse_id=stock_in.warehouse_id,
                    ingredient_id=item.ingredient_id,
                    batch_number=item.batch_number,
                    status='正常'
                ).first()

                if existing_inventory:
                    # 更新现有库存
                    existing_inventory.quantity += item.quantity
                else:
                    # 创建新库存记录
                    new_inventory = Inventory(
                        warehouse_id=stock_in.warehouse_id,
                        storage_location_id=item.storage_location_id,
                        ingredient_id=item.ingredient_id,
                        batch_number=item.batch_number,
                        quantity=item.quantity,
                        unit=item.unit,
                        production_date=item.production_date,
                        expiry_date=item.expiry_date,
                        supplier_id=item.supplier_id,
                        status='正常',
                        notes=f'由入库单 {stock_in.stock_in_number} 创建'
                    )
                    db.session.add(new_inventory)

            # 更新入库单状态为已入库
            stock_in.status = '已入库'
            db.session.commit()

            # 更新采购订单状态为已确认（如果是待确认状态）
            if purchase_order.status == '待确认':
                purchase_order.status = '已确认'
                purchase_order.confirmed_at = datetime.now()
                db.session.commit()

            flash('已从采购订单创建入库单并自动完成入库流程', 'success')
            return redirect(url_for('stock_in.view', id=stock_in.id))

        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"自动执行入库时出错: {str(e)}")
            flash(f'创建入库单成功，但自动入库失败: {str(e)}', 'warning')
            return redirect(url_for('stock_in_wizard.wizard_edit', id=stock_in.id))

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"从采购订单创建入库单时出错: {str(e)}")
        flash(f'从采购订单创建入库单时出错: {str(e)}', 'danger')
        return redirect(url_for('purchase_order.view', id=purchase_order_id))

@stock_in_wizard_bp.route('/stock-in/<int:id>/update-basic', methods=['POST'])
@login_required
def update_basic(id):
    """更新入库单基本信息"""
    try:
        stock_in = StockIn.query.get_or_404(id)

        # 检查用户是否有权限操作该入库单
        if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限编辑该区域的入库单'
            })

        # 检查权限
        if stock_in.status != '待审核':
            return jsonify({
                'success': False,
                'message': '只有待审核状态的入库单可以编辑'
            })

        data = request.json

        # 更新入库单
        stock_in.warehouse_id = data['warehouse_id']
        stock_in.stock_in_type = data['stock_in_type']
        stock_in.stock_in_date = datetime.strptime(data['stock_in_date'], '%Y-%m-%d')
        stock_in.notes = data.get('notes', '')

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '入库单基本信息更新成功',
            'stock_in_id': stock_in.id
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新入库单基本信息时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'更新入库单基本信息时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/api/purchase-orders/available')
@login_required
@school_required
def api_available_purchase_orders(user_area):
    """获取可用于入库的采购订单列表（API接口）"""
    try:
        current_app.logger.info(f"用户 {current_user.username} 请求获取可用采购订单，学校: {user_area.name}")

        # 查询状态为"已确认"或"准备入库"的采购订单，且未创建入库单的
        query = PurchaseOrder.query.filter(
            PurchaseOrder.area_id == user_area.id,
            PurchaseOrder.status.in_(['已确认', '准备入库'])
        )

        # 排序并获取结果
        orders = query.order_by(PurchaseOrder.order_date.desc()).limit(50).all()

        # 构建返回数据
        result_data = []
        for order in orders:
            # 获取供应商信息
            supplier_name = order.supplier.name if order.supplier else '自购'

            # 格式化创建时间
            created_at = order.created_at.strftime('%Y-%m-%d %H:%M') if order.created_at else order.order_date.strftime('%Y-%m-%d')

            # 检查是否已经创建了入库单
            existing_stock_in = StockIn.query.filter_by(purchase_order_id=order.id).first()
            if existing_stock_in:
                continue  # 跳过已经创建入库单的采购订单

            result_data.append({
                'id': order.id,
                'order_number': order.order_number,
                'status': order.status,
                'supplier_name': supplier_name,
                'created_at': created_at,
                'order_date': order.order_date.strftime('%Y-%m-%d'),
                'delivery_date': order.delivery_date.strftime('%Y-%m-%d') if order.delivery_date else None,
                'total_amount': float(order.total_amount) if order.total_amount else None,
                'notes': order.notes or ''
            })

        current_app.logger.info(f"找到 {len(result_data)} 个可用的采购订单")

        return jsonify({
            'success': True,
            'data': result_data,
            'count': len(result_data)
        })

    except Exception as e:
        current_app.logger.error(f"获取可用采购订单失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取采购订单列表失败，请稍后重试'
        }), 500

@stock_in_wizard_bp.route('/stock-in/<int:id>/items-json')
@login_required
def get_items_json(id):
    """获取入库单食材列表"""
    try:
        stock_in = StockIn.query.get_or_404(id)

        # 检查用户是否有权限查看该入库单
        if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限查看该区域的入库单'
            })

        items = []
        for item in stock_in.stock_in_items:
            # 检查该食材是否需要检验检疫
            needs_inspection = False
            if item.ingredient.category_rel:
                needs_inspection = item.ingredient.category_rel.needs_inspection

            # 获取存储位置名称
            storage_location_name = None
            if item.storage_location:
                storage_location_name = item.storage_location.name

            # 获取供应商名称
            supplier_name = None
            if item.supplier:
                supplier_name = item.supplier.name

            items.append({
                'id': item.id,
                'ingredient_id': item.ingredient_id,
                'ingredient_name': item.ingredient.name,
                'batch_number': item.batch_number,
                'quantity': item.quantity,
                'unit': item.unit,
                'production_date': item.production_date.strftime('%Y-%m-%d') if item.production_date else None,
                'expiry_date': item.expiry_date.strftime('%Y-%m-%d') if item.expiry_date else None,
                'storage_location_id': item.storage_location_id,
                'storage_location_name': storage_location_name,
                'supplier_id': item.supplier_id,
                'supplier_name': supplier_name,
                'unit_price': item.unit_price,
                'needs_inspection': needs_inspection
            })

        return jsonify({
            'success': True,
            'data': items
        })

    except Exception as e:
        current_app.logger.error(f"获取入库单食材列表时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取入库单食材列表时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/<int:id>/save-items', methods=['POST'])
@login_required
def save_items(id):
    """保存入库单食材信息"""
    try:
        stock_in = StockIn.query.get_or_404(id)

        # 检查用户是否有权限操作该入库单
        if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限编辑该区域的入库单'
            })

        # 检查权限
        if stock_in.status != '待审核':
            return jsonify({
                'success': False,
                'message': '只有待审核状态的入库单可以编辑'
            })

        data = request.json
        items = data.get('items', [])

        for item_data in items:
            if item_data.get('id'):
                # 更新已有食材
                item = StockInItem.query.get(item_data['id'])
                if item and item.stock_in_id == id:
                    item.storage_location_id = item_data['storage_location_id']
                    item.batch_number = item_data['batch_number']
                    item.quantity = item_data['quantity']
                    item.unit = item_data['unit']
                    item.production_date = datetime.strptime(item_data['production_date'], '%Y-%m-%d')
                    item.expiry_date = datetime.strptime(item_data['expiry_date'], '%Y-%m-%d')
                    item.unit_price = item_data.get('unit_price')
                    item.supplier_id = item_data.get('supplier_id')
            else:
                # 创建新食材
                # 这里需要实现添加新食材的逻辑
                pass

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '入库单食材信息保存成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"保存入库单食材信息时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'保存入库单食材信息时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/<int:id>/remove-item/<int:item_id>', methods=['POST'])
@login_required
def remove_item(id, item_id):
    """删除入库单食材"""
    try:
        stock_in = StockIn.query.get_or_404(id)

        # 检查用户是否有权限操作该入库单
        if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限编辑该区域的入库单'
            })

        # 检查权限
        if stock_in.status != '待审核':
            return jsonify({
                'success': False,
                'message': '只有待审核状态的入库单可以编辑'
            })

        item = StockInItem.query.get_or_404(item_id)

        # 检查食材是否属于该入库单
        if item.stock_in_id != id:
            return jsonify({
                'success': False,
                'message': '该食材不属于当前入库单'
            })

        db.session.delete(item)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '入库单食材删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除入库单食材时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除入库单食材时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/<int:id>/inspection-items-json')
@login_required
def get_inspection_items_json(id):
    """获取需要检验的食材列表"""
    try:
        stock_in = StockIn.query.get_or_404(id)

        # 检查用户是否有权限查看该入库单
        if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限查看该区域的入库单'
            })

        inspection_items = []
        for item in stock_in.stock_in_items:
            # 检查该食材是否需要检验检疫
            needs_inspection = False
            if item.ingredient.category_rel:
                needs_inspection = item.ingredient.category_rel.needs_inspection

            if needs_inspection:
                # 查询是否已有检验记录
                inspection = IngredientInspection.query.filter_by(stock_in_item_id=item.id).first()

                inspection_items.append({
                    'id': item.id,
                    'ingredient_id': item.ingredient_id,
                    'ingredient_name': item.ingredient.name,
                    'batch_number': item.batch_number,
                    'inspection_type': inspection.inspection_type if inspection else None,
                    'result': inspection.result if inspection else None,
                    'notes': inspection.notes if inspection else None
                })

        return jsonify({
            'success': True,
            'data': inspection_items
        })

    except Exception as e:
        current_app.logger.error(f"获取需要检验的食材列表时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取需要检验的食材列表时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/<int:id>/save-inspections', methods=['POST'])
@login_required
def save_inspections(id):
    """保存检验信息"""
    try:
        stock_in = StockIn.query.get_or_404(id)

        # 检查用户是否有权限操作该入库单
        if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限编辑该区域的入库单'
            })

        # 检查权限
        if stock_in.status != '待审核':
            return jsonify({
                'success': False,
                'message': '只有待审核状态的入库单可以编辑'
            })

        data = request.json
        inspections = data.get('inspections', [])

        for inspection_data in inspections:
            item_id = inspection_data['item_id']
            item = StockInItem.query.get(item_id)

            if item and item.stock_in_id == id:
                # 查询是否已有检验记录
                inspection = IngredientInspection.query.filter_by(stock_in_item_id=item_id).first()

                if inspection:
                    # 更新已有检验记录
                    inspection.inspection_type = inspection_data['inspection_type']
                    inspection.result = inspection_data['result']
                    inspection.notes = inspection_data.get('notes', '')
                else:
                    # 创建新检验记录
                    inspection = IngredientInspection(
                        stock_in_item_id=item_id,
                        inspector_id=current_user.id,
                        inspection_date=datetime.now().date(),
                        inspection_type=inspection_data['inspection_type'],
                        result=inspection_data['result'],
                        notes=inspection_data.get('notes', '')
                    )
                    db.session.add(inspection)

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '检验信息保存成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"保存检验信息时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'保存检验信息时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/<int:id>/upload-document', methods=['POST'])
@login_required
def upload_document(id):
    """上传文档"""
    try:
        stock_in = StockIn.query.get_or_404(id)

        # 检查用户是否有权限操作该入库单
        if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限编辑该区域的入库单'
            })

        # 检查权限
        if stock_in.status != '待审核':
            return jsonify({
                'success': False,
                'message': '只有待审核状态的入库单可以编辑'
            })

        # 获取表单数据
        document_type = request.form.get('document_type')
        supplier_id = request.form.get('supplier_id')
        notes = request.form.get('notes', '')

        # 获取上传的文件
        file = request.files.get('file')

        if not file:
            return jsonify({
                'success': False,
                'message': '请选择要上传的文件'
            })

        # 保存文件
        filename = secure_filename(file.filename)
        # 生成唯一文件名
        unique_filename = f"{uuid.uuid4().hex}_{filename}"

        # 确保上传目录存在
        upload_dir = os.path.join(current_app.config['UPLOAD_FOLDER'], 'stock_in_documents')
        os.makedirs(upload_dir, exist_ok=True)

        # 保存文件
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)

        # 创建文档记录
        document = StockInDocument(
            stock_in_id=id,
            document_type=document_type,
            file_path=os.path.join('uploads', 'stock_in_documents', unique_filename),
            supplier_id=supplier_id if supplier_id else None,
            notes=notes
        )

        db.session.add(document)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '文档上传成功',
            'document_id': document.id
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"上传文档时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'上传文档时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/<int:id>/documents-json')
@login_required
def get_documents_json(id):
    """获取文档列表"""
    try:
        stock_in = StockIn.query.get_or_404(id)

        # 检查用户是否有权限查看该入库单
        if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限查看该区域的入库单'
            })

        documents = []
        for doc in StockInDocument.query.filter_by(stock_in_id=id).all():
            # 获取供应商名称
            supplier_name = None
            if doc.supplier:
                supplier_name = doc.supplier.name

            documents.append({
                'id': doc.id,
                'document_type': doc.document_type,
                'file_path': doc.file_path,
                'supplier_id': doc.supplier_id,
                'supplier_name': supplier_name,
                'notes': doc.notes,
                'created_at': doc.created_at.strftime('%Y-%m-%d %H:%M:%S')
            })

        return jsonify({
            'success': True,
            'data': documents
        })

    except Exception as e:
        current_app.logger.error(f"获取文档列表时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取文档列表时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/<int:id>/delete-document/<int:doc_id>', methods=['POST'])
@login_required
def delete_document(id, doc_id):
    """删除文档"""
    try:
        stock_in = StockIn.query.get_or_404(id)

        # 检查权限
        if stock_in.status != '待审核':
            return jsonify({
                'success': False,
                'message': '只有待审核状态的入库单可以编辑'
            })

        document = StockInDocument.query.get_or_404(doc_id)

        # 检查文档是否属于该入库单
        if document.stock_in_id != id:
            return jsonify({
                'success': False,
                'message': '该文档不属于当前入库单'
            })

        # 删除文件
        if document.file_path and os.path.exists(os.path.join(current_app.root_path, '..', document.file_path)):
            os.remove(os.path.join(current_app.root_path, '..', document.file_path))

        db.session.delete(document)
        db.session.commit()

        return jsonify({
            'success': True,
            'message': '文档删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除文档时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'删除文档时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/<int:id>/json')
@login_required
def get_stock_in_json(id):
    """获取入库单信息"""
    try:
        stock_in = StockIn.query.get_or_404(id)

        # 获取仓库名称
        warehouse_name = None
        if stock_in.warehouse:
            warehouse_name = stock_in.warehouse.name

        # 获取操作员名称
        operator_name = None
        if stock_in.operator:
            operator_name = stock_in.operator.username

        # 获取检查员名称
        inspector_name = None
        if stock_in.inspector:
            inspector_name = stock_in.inspector.username

        data = {
            'id': stock_in.id,
            'stock_in_number': stock_in.stock_in_number,
            'warehouse_id': stock_in.warehouse_id,
            'warehouse_name': warehouse_name,
            'stock_in_date': stock_in.stock_in_date.strftime('%Y-%m-%d'),
            'stock_in_type': stock_in.stock_in_type,
            'operator_id': stock_in.operator_id,
            'operator_name': operator_name,
            'inspector_id': stock_in.inspector_id,
            'inspector_name': inspector_name,
            'status': stock_in.status,
            'notes': stock_in.notes,
            'created_at': stock_in.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }

        return jsonify({
            'success': True,
            'data': data
        })

    except Exception as e:
        current_app.logger.error(f"获取入库单信息时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取入库单信息时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/<int:id>/inspections-json')
@login_required
def get_inspections_json(id):
    """获取检验信息列表"""
    try:
        stock_in = StockIn.query.get_or_404(id)

        inspections = []
        for item in stock_in.stock_in_items:
            # 查询检验记录
            inspection = IngredientInspection.query.filter_by(stock_in_item_id=item.id).first()

            if inspection:
                inspections.append({
                    'id': inspection.id,
                    'item_id': item.id,
                    'ingredient_id': item.ingredient_id,
                    'ingredient_name': item.ingredient.name,
                    'inspection_type': inspection.inspection_type,
                    'result': inspection.result,
                    'notes': inspection.notes,
                    'inspector_id': inspection.inspector_id,
                    'inspector_name': inspection.inspector.username if inspection.inspector else None,
                    'inspection_date': inspection.inspection_date.strftime('%Y-%m-%d')
                })

        return jsonify({
            'success': True,
            'data': inspections
        })

    except Exception as e:
        current_app.logger.error(f"获取检验信息列表时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取检验信息列表时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/<int:id>/submit', methods=['POST'])
@login_required
def submit_stock_in(id):
    """提交入库单"""
    try:
        stock_in = StockIn.query.get_or_404(id)

        # 检查用户是否有权限操作该入库单
        if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
            return jsonify({
                'success': False,
                'message': '您没有权限编辑该区域的入库单'
            })

        # 检查权限
        if stock_in.status != '待审核':
            return jsonify({
                'success': False,
                'message': '只有待审核状态的入库单可以提交'
            })

        # 检查是否有食材明细
        if stock_in.stock_in_items.count() == 0:
            return jsonify({
                'success': False,
                'message': '入库单必须至少有一个食材明细'
            })

        # 更新入库单状态
        stock_in.status = '已审核'
        stock_in.inspector_id = current_user.id

        db.session.commit()

        return jsonify({
            'success': True,
            'message': '入库单提交成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"提交入库单时出错: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'提交入库单时出错: {str(e)}'
        })

@stock_in_wizard_bp.route('/stock-in/<int:id>/report')
@login_required
def generate_report(id):
    """生成入库报告"""
    try:
        # 检查入库单是否存在
        stock_in = StockIn.query.get_or_404(id)

        # 检查用户是否有权限查看该入库单
        if not current_user.can_access_area_by_id(stock_in.warehouse.area_id):
            flash('您没有权限查看该区域的入库单', 'danger')
            return redirect(url_for('stock_in.index'))

        # 导入PDF生成器
        from app.utils.pdf_generator import generate_stock_in_report

        # 生成PDF报告
        pdf_path = generate_stock_in_report(id)

        # 返回PDF文件
        return redirect(url_for('static', filename=pdf_path))

    except Exception as e:
        current_app.logger.error(f"生成入库报告时出错: {str(e)}")
        flash(f'生成入库报告时出错: {str(e)}', 'danger')
        return redirect(url_for('stock_in.view', id=id))



