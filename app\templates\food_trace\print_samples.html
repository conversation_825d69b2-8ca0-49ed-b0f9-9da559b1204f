<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>留样记录打印</title>
    <style>
        @media print {
            @page {
                size: A4;
                margin: 0.5cm;  /* 进一步减小页面边距 */
            }
            body {
                margin: 0;
                padding: 0;
                font-family: Sim<PERSON><PERSON>, "Microsoft YaHei", sans-serif;
                font-size: 11px;  /* 增加基础字体大小 */
            }
            .no-print {
                display: none !important;
            }
            .page-break {
                page-break-after: always;
            }
            .sample-card {
                page-break-inside: avoid;  /* 避免标签被分页 */
            }
        }

        body {
            font-family: SimSun, "Microsoft YaHei", sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: #000;
            background: #fff;
            margin: 0;
            padding: 20px;
        }

        .print-header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #000;
        }

        .print-title {
            font-size: 18px;  /* 从24px减小到18px */
            font-weight: bold;
            margin: 0 0 5px;
        }

        .print-info {
            margin-bottom: 10px;
            font-size: 10px;  /* 减小字体大小 */
        }

        .info-row {
            display: flex;
            margin-bottom: 5px;
        }

        .info-label {
            width: 70px;  /* 从100px减小到70px */
            font-weight: bold;
        }

        .info-value {
            flex: 1;
        }

        .sample-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: flex-start;  /* 改为从左开始排列 */
            gap: 0.5%;  /* 添加间隙 */
        }

        .sample-card {
            width: 29%;
            border: 1px solid #000;
            margin-bottom: 12px;
            padding: 6px;
            box-sizing: border-box;
            font-size: 11px;  /* 进一步增加字体大小 */
        }

        .sample-header {
            border-bottom: 1px solid #000;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }

        .sample-title {
            font-weight: bold;
            font-size: 14px;  /* 进一步增加字体大小 */
            margin-bottom: 2.4px;
        }

        .sample-ingredients {
            font-size: 10px;  /* 进一步增加字体大小 */
            color: #555;
            font-style: italic;
            margin-top: 2.4px;
            border-top: 1px dashed #ccc;
            padding-top: 1.2px;
        }

        .sample-content {
            display: flex;
        }

        .sample-info {
            flex: 1;
        }

        .sample-info-row {
            margin-bottom: 2px;
            line-height: 1.2;
            font-size: 10px;  /* 增加字体大小 */
        }

        .sample-image {
            width: 50px;
            height: 50px;
            border: 1px dashed #000;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 3px;
            font-size: 9px;  /* 增加字体大小 */
        }

        .sample-footer {
            margin-top: 5px;
            border-top: 1px solid #000;
            padding-top: 3px;
            display: flex;
            justify-content: space-between;
            font-size: 9px;  /* 增加字体大小 */
        }

        .signature {
            display: flex;
        }

        .signature-label {
            margin-right: 3px;
        }

        .signature-line {
            width: 50px;  /* 从100px减小到50px */
            border-bottom: 1px solid #000;
        }

        .print-footer {
            margin-top: 20px;  /* 从40px减小到20px */
            padding-top: 10px;  /* 从20px减小到10px */
            border-top: 1px solid #000;
            text-align: center;
            font-size: 8px;  /* 减小字体大小 */
        }

        .no-print {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #007bff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .no-print:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <button class="no-print" onclick="window.print()">打印留样记录</button>

    <div class="print-header">
        <h1 class="print-title">{{ project_name }} - 食品留样记录</h1>
    </div>

    <div class="print-info">
        <div class="info-row">
            <span class="info-label">区域：</span>
            <span class="info-value">{{ area.name if area else '-' }}</span>
            <span class="info-label">日期：</span>
            <span class="info-value">{{ trace_date }}</span>
        </div>
        <div class="info-row">
            <span class="info-label">餐次：</span>
            <span class="info-value">{{ meal_type }}</span>
            <span class="info-label">打印时间：</span>
            <span class="info-value">{{ current_time.strftime('%Y-%m-%d %H:%M:%S') }}</span>
        </div>
    </div>

    <div class="sample-grid">
        {% for recipe_data in recipes_with_samples %}
        <div class="sample-card">
            <div class="sample-header">
                <div class="sample-title">{{ recipe_data.recipe.name }}</div>
                <div>{{ trace_date }} {{ meal_type }}</div>
                <div class="sample-ingredients">{{ recipe_data.main_ingredients }}</div>
            </div>
            <div class="sample-content">
                <div class="sample-info">
                    <div class="sample-info-row">
                        <strong>留样编号：</strong>
                        {% if recipe_data.sample %}
                            {{ recipe_data.sample.sample_number }}
                        {% else %}
                            {% set meal_code = '01' if meal_type == '早餐' else ('02' if meal_type == '午餐' else '03') %}
                            {% set date_code = trace_date.replace('-', '') %}
                            {% set recipe_code = recipe_data.recipe.id|string %}
                            LS-{{ date_code }}-{{ meal_code }}-{{ recipe_code }}
                        {% endif %}
                    </div>
                    <div class="sample-info-row">
                        <strong>留样数量：</strong> {{ recipe_data.sample.sample_quantity if recipe_data.sample else sample_quantity }} {{ recipe_data.sample.sample_unit if recipe_data.sample else 'g' }}
                    </div>
                    <div class="sample-info-row">
                        <strong>留样时间：</strong> {{ recipe_data.sample.start_time.strftime('%Y-%m-%d %H:%M') if recipe_data.sample and recipe_data.sample.start_time else current_time.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                    <div class="sample-info-row">
                        <strong>销毁时间：</strong> {{ recipe_data.sample.end_time.strftime('%Y-%m-%d %H:%M') if recipe_data.sample and recipe_data.sample.end_time else end_time.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                    <div class="sample-info-row">
                        <strong>存放位置：</strong> {{ recipe_data.sample.storage_location if recipe_data.sample else storage_location }}
                    </div>
                    <div class="sample-info-row">
                        <strong>存放温度：</strong> {{ recipe_data.sample.storage_temperature if recipe_data.sample else storage_temp }}℃
                    </div>
                </div>
                <div class="sample-image">
                    {% if recipe_data.sample and recipe_data.sample.sample_image %}
                    <img src="{{ url_for('static', filename=recipe_data.sample.sample_image) }}" alt="留样图片" style="max-width: 100%; max-height: 100%;">
                    {% else %}
                    留样图片
                    {% endif %}
                </div>
            </div>
            <div class="sample-footer">
                <div class="signature">
                    <div class="signature-label">留样人：</div>
                    <div class="signature-line"></div>
                </div>
                <div class="signature">
                    <div class="signature-label">检查人：</div>
                    <div class="signature-line"></div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <div class="print-footer">
        <p>注：每种食品留样量不少于50g，留样时间为48小时</p>
    </div>

    <script nonce="{{ csp_nonce }}">
        window.onload = function() {
            // 自动聚焦
            window.focus();
        };
    </script>
</body>
</html>
